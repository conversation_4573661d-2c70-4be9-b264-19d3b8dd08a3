<?php

namespace App\Console\Commands;

use App\Models\Sale;
use Illuminate\Console\Command;

class TestChart extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:chart';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test chart data for sales by month';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== TESTE DO GRÁFICO DE VENDAS POR MÊS ===');
        $this->newLine();

        $this->info('Dados dos últimos 12 meses (CORRIGIDO):');

        for ($i = 11; $i >= 0; $i--) {
            $date = now()->startOfMonth()->subMonths($i);
            $monthSales = Sale::whereMonth('sale_date', $date->month)
                ->whereYear('sale_date', $date->year)
                ->sum('total');

            $label = $date->format('M/Y');
            $this->line("Mês: $label | Vendas: R$ " . number_format($monthSales, 2, ',', '.'));
        }

        $this->newLine();
        $this->info('=== COMPARAÇÃO DE MÉTODOS ===');

        $this->warn('Método antigo (problemático):');
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $label = $date->format('M/Y');
            $this->line("  $label");
        }

        $this->newLine();
        $this->info('Método novo (corrigido):');
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->startOfMonth()->subMonths($i);
            $label = $date->format('M/Y');
            $this->line("  $label");
        }
    }
}
