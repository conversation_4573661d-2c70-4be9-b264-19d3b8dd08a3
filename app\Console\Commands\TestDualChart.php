<?php

namespace App\Console\Commands;

use App\Models\Sale;
use Illuminate\Console\Command;

class TestDualChart extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:dual-chart';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test dual chart data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== TESTE GRÁFICO DUPLO - VALOR E QUANTIDADE ===');
        $this->newLine();

        for ($i = 11; $i >= 0; $i--) {
            $date = now()->startOfMonth()->subMonths($i);

            // Buscar vendas e quantidade em uma única consulta
            $monthData = Sale::whereMonth('sale_date', $date->month)
                ->whereYear('sale_date', $date->year)
                ->selectRaw('SUM(total) as total_sales, COUNT(*) as total_count')
                ->first();

            $totalSales = (float) ($monthData->total_sales ?? 0);
            $totalCount = (int) ($monthData->total_count ?? 0);
            $label = $date->format('M/Y');

            $this->line("$label | Valor: R$ " . number_format($totalSales, 2, ',', '.') . " | Quantidade: $totalCount vendas");
        }
    }
}
